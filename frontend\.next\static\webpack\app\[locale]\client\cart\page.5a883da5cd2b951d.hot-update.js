"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/cart/page",{

/***/ "(app-pages-browser)/./src/app/services/cartService.js":
/*!*****************************************!*\
  !*** ./src/app/services/cartService.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst cartService = {\n    // Get the user's cart\n    getCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/cart/get-cart\", {\n            withCredentials: true\n        }),\n    // Add an item to the cart\n    addItemToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/add-item\", data, {\n            withCredentials: true\n        }),\n    // Remove an item from the cart\n    removeItemFromCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/remove-item\", data, {\n            withCredentials: true\n        }),\n    // Clear the user's cart\n    clearCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/clear\", {\n            withCredentials: true\n        }),\n    // Update item quantity in the cart\n    updateCartItemQuantity: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item\", data, {\n            withCredentials: true\n        }),\n    // Update item period in the cart\n    updateItemPeriod: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item-period\", data, {\n            withCredentials: true\n        }),\n    // Add method to remove domain from cart\n    removeDomainFromCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/remove-domain\", data, {\n            withCredentials: true\n        }),\n    // Add method to update domain period\n    updateDomainPeriod: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/update-domain-period\", data, {\n            withCredentials: true\n        }),\n    // Add method to update domain options\n    updateDomainOptions: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/update-domain-options\", data, {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (cartService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/cartService.js\n"));

/***/ })

});