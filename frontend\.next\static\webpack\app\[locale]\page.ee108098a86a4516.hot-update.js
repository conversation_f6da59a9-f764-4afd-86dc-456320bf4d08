"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const [privacyProtection, setPrivacyProtection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Privacy protection option\n    const [autoRenew, setAutoRenew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Auto-renewal option\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period,\n                privacyProtection: privacyProtection,\n                autoRenew: autoRenew\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 232,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"0dlu9g0/oDsgZKGxTTSKKJ9GQXc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});