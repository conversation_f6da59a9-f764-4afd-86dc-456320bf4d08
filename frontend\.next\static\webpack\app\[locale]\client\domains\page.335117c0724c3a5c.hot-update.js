"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/page.jsx":
/*!**************************************************!*\
  !*** ./src/app/[locale]/client/domains/page.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Lock,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Lock,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Lock,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Lock,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Lock,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _app_services_orderService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/orderService */ \"(app-pages-browser)/./src/app/services/orderService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DomainManagementPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"client.domainWrapper\");\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const getUserDomains = async ()=>{\n            try {\n                setLoading(true);\n                // Get real domain data from the API\n                const res = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                console.log(\"Domain data:\", res.data);\n                if (res.data && res.data.domains) {\n                    setDomains(res.data.domains);\n                } else {\n                    setDomains([]);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domains\", error);\n                setLoading(false);\n                // If there's an error, set domains to empty array\n                setDomains([]);\n            }\n        };\n        getUserDomains();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    if (!(domains === null || domains === void 0 ? void 0 : domains.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-20 w-20 bg-blue-100 rounded-full flex items-center justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-10 w-10 text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: dt(\"no_domains\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                    className: \"text-gray-600 text-center max-w-md mb-8\",\n                    children: dt(\"browse_domains_message\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    size: \"lg\",\n                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/domains\"),\n                    children: [\n                        dt(\"browse_domains\"),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                    variant: \"h1\",\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: dt(\"your_domains\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: dt(\"manage_domains\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                            onClick: ()=>router.push(\"/domains\"),\n                            children: [\n                                dt(\"browse_domains\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-500\",\n                                                children: dt(\"domain_name\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-500\",\n                                                children: dt(\"status\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-500\",\n                                                children: dt(\"registration_date\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-500\",\n                                                children: dt(\"expiry_date\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-4 text-right text-sm font-medium text-gray-500\",\n                                                children: dt(\"manage\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"divide-y divide-gray-200\",\n                                    children: domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: domain.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: domain.registrar\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: dt(domain.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                    children: new Date(domain.registrationDate).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                    children: new Date(domain.expiryDate).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        size: \"sm\",\n                                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(domain.id)),\n                                                        children: dt(\"manage\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, domain.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                className: \"ml-3 font-medium text-gray-900\",\n                                                children: dt(\"dns_settings\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: t(\"manage_dns_records_description\", {\n                                            defaultValue: \"Configure DNS records, nameservers, and more for your domains.\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outlined\",\n                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                        onClick: ()=>router.push(\"/client/domains/dns\"),\n                                        children: dt(\"manage_dns_records\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                className: \"ml-3 font-medium text-gray-900\",\n                                                children: dt(\"domain_locks\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: t(\"domain_locks_description\", {\n                                            defaultValue: \"Protect your domains from unauthorized transfers and changes.\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outlined\",\n                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                        onClick: ()=>router.push(\"/client/domains/locks\"),\n                                        children: t(\"manage_locks\", {\n                                            defaultValue: \"Manage Locks\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Lock_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                className: \"ml-3 font-medium text-gray-900\",\n                                                children: dt(\"whois_privacy\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: t(\"whois_privacy_description\", {\n                                            defaultValue: \"Protect your personal information from being publicly visible in WHOIS records.\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outlined\",\n                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                        onClick: ()=>router.push(\"/client/domains/privacy\"),\n                                        children: t(\"manage_privacy\", {\n                                            defaultValue: \"Manage Privacy\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\page.jsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainManagementPage, \"gMTDiDDVHZ+wuBdkndIxnYZuUOk=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DomainManagementPage;\nvar _c;\n$RefreshReg$(_c, \"DomainManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: function() { return /* binding */ API_GOOGLE_MAP_KEY; },\n/* harmony export */   BACKEND_URL: function() { return /* binding */ BACKEND_URL; },\n/* harmony export */   COOKIE_DOMAIN: function() { return /* binding */ COOKIE_DOMAIN; },\n/* harmony export */   FRONTEND_URL: function() { return /* binding */ FRONTEND_URL; },\n/* harmony export */   REACT_APP_GG_APP_ID: function() { return /* binding */ REACT_APP_GG_APP_ID; }\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = false;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29uZmlnL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsTUFBTUEsYUFBYTtBQUNuQixNQUFNQyxjQUFjO0FBRXBCLE1BQU1DLFVBQVU7QUFDaEIsTUFBTUMsV0FBVztBQUVqQixNQUFNQyxTQUFTO0FBQ1IsTUFBTUMsY0FBY0QsU0FBU0YsVUFBVUYsV0FBVztBQUNsRCxNQUFNTSxlQUFlRixTQUFTRCxXQUFXRixZQUFZO0FBQ3JELE1BQU1NLGdCQUFnQkgsU0FBUywwQkFBMEIsWUFBWTtBQUVyRSxNQUFNSSxzQkFDWCwyRUFBMkU7QUFDdEUsTUFBTUMscUJBQXFCLDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcz9iMTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhY2tlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6NTAwMlwiO1xyXG5jb25zdCBmcm9udGVuZERldiA9IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCI7XHJcblxyXG5jb25zdCBiYWNrZW5kID0gXCJodHRwczovL2FwaS56dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5jb25zdCBmcm9udGVuZCA9IFwiaHR0cHM6Ly96dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5cclxuY29uc3QgaXNQcm9kID0gZmFsc2U7XHJcbmV4cG9ydCBjb25zdCBCQUNLRU5EX1VSTCA9IGlzUHJvZCA/IGJhY2tlbmQgOiBiYWNrZW5kRGV2O1xyXG5leHBvcnQgY29uc3QgRlJPTlRFTkRfVVJMID0gaXNQcm9kID8gZnJvbnRlbmQgOiBmcm9udGVuZERldjtcclxuZXhwb3J0IGNvbnN0IENPT0tJRV9ET01BSU4gPSBpc1Byb2QgPyBcIi56dGVjaGVuZ2luZWVyaW5nLmNvbVwiIDogXCJsb2NhbGhvc3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBSRUFDVF9BUFBfR0dfQVBQX0lEID1cclxuICBcIjQ4MDk4NzM4NDQ1OS1oM2NpZTJ2Y3NocDA5dnBodXZuc2hjY3FwcmNvM2Ziby5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbVwiO1xyXG5leHBvcnQgY29uc3QgQVBJX0dPT0dMRV9NQVBfS0VZID0gXCJBSXphU3lBNXBHeTNVRUt3YmdqVVktNzJSbW9SN25wRXExYl91ZjBcIjtcclxuIl0sIm5hbWVzIjpbImJhY2tlbmREZXYiLCJmcm9udGVuZERldiIsImJhY2tlbmQiLCJmcm9udGVuZCIsImlzUHJvZCIsIkJBQ0tFTkRfVVJMIiwiRlJPTlRFTkRfVVJMIiwiQ09PS0lFX0RPTUFJTiIsIlJFQUNUX0FQUF9HR19BUFBfSUQiLCJBUElfR09PR0xFX01BUF9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/config/constant.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(app-pages-browser)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, config);\n    },\n    post: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config);\n    },\n    put: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config);\n    },\n    delete: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config);\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2FwaVNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7QUFBNEM7QUFFNUMsTUFBTUMsYUFBYTtJQUNqQkMsS0FBSyxTQUFDQztZQUFLQywwRUFBUyxDQUFDO2VBQU1KLHNEQUFhQSxDQUFDRSxHQUFHLENBQUNDLEtBQUtDOztJQUVsREMsTUFBTSxTQUFDRjtZQUFLRyx3RUFBTyxDQUFDLEdBQUdGLDBFQUFTLENBQUM7ZUFBTUosc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUY7O0lBRXJFRyxLQUFLLFNBQUNKO1lBQUtHLHdFQUFPLENBQUMsR0FBR0YsMEVBQVMsQ0FBQztlQUFNSixzREFBYUEsQ0FBQ08sR0FBRyxDQUFDSixLQUFLRyxNQUFNRjs7SUFFbkVJLFFBQVEsU0FBQ0w7WUFBS0MsMEVBQVMsQ0FBQztlQUFNSixzREFBYUEsQ0FBQ1EsTUFBTSxDQUFDTCxLQUFLQzs7QUFDMUQ7QUFFQSwrREFBZUgsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSBcIi4vYXhpb3NJbnN0YW5jZVwiO1xyXG5cclxuY29uc3QgYXBpU2VydmljZSA9IHtcclxuICBnZXQ6ICh1cmwsIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIGNvbmZpZyksXHJcblxyXG4gIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gIHB1dDogKHVybCwgZGF0YSA9IHt9LCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5wdXQodXJsLCBkYXRhLCBjb25maWcpLFxyXG5cclxuICBkZWxldGU6ICh1cmwsIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmRlbGV0ZSh1cmwsIGNvbmZpZyksXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBhcGlTZXJ2aWNlO1xyXG4iXSwibmFtZXMiOlsiYXhpb3NJbnN0YW5jZSIsImFwaVNlcnZpY2UiLCJnZXQiLCJ1cmwiLCJjb25maWciLCJwb3N0IiwiZGF0YSIsInB1dCIsImRlbGV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/apiService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/domainMngService.js":
/*!**********************************************!*\
  !*** ./src/app/services/domainMngService.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst domainMngService = {\n    // Customer management\n    customerSignup: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/customer-signup\", data, {\n            withCredentials: true\n        }),\n    // Domain operations\n    checkDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkIdnDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-idn-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkPremiumDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-premium-domain\", {\n            params,\n            withCredentials: true\n        }),\n    suggestDomainNames: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/suggest-names\", {\n            params,\n            withCredentials: true\n        }),\n    // Pricing information\n    getDNPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-dn-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Get reseller pricing\n    getResellerPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-reseller-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Add domain to cart\n    addDomainToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/add-domain-to-cart\", data, {\n            withCredentials: true\n        }),\n    // Add the new comprehensive search method\n    // Expected params: { params: \"domain-name-string\" }\n    searchDomains: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/search-domains\", {\n            params,\n            withCredentials: true\n        }),\n    // Domain Management - User Domains\n    getUserDomains: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/order/get-domain-orders\", {\n            withCredentials: true\n        }),\n    // Get domain details\n    getDomainDetails: (domainName)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        }),\n    // Get domain details by name (from registration system)\n    getDomainDetailsByName: function(domainName) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"OrderDetails\";\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details-by-name\", {\n            params: {\n                domainName,\n                options\n            },\n            withCredentials: true\n        });\n    },\n    // Renew domain\n    renewDomain: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/renew-domain\", data, {\n            withCredentials: true\n        }),\n    // Get domain order ID\n    getDomainOrderId: (domainName)=>{\n        console.log(\"\\uD83D\\uDD27 Frontend service - getDomainOrderId called with:\", domainName);\n        console.log(\"\\uD83D\\uDD27 Frontend service - params object:\", {\n            domainName\n        });\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-order-id\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        });\n    },\n    // Get customer default nameservers\n    getCustomerDefaultNameservers: (customerId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/customer-default-nameservers\", {\n            params: customerId ? {\n                customerId\n            } : {},\n            withCredentials: true\n        }),\n    // Modify nameservers\n    modifyNameservers: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/modify-nameservers\", data, {\n            withCredentials: true\n        }),\n    // Enable privacy protection\n    enablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/enable-privacy\", data, {\n            withCredentials: true\n        }),\n    // Disable privacy protection\n    disablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/disable-privacy\", data, {\n            withCredentials: true\n        }),\n    getDomainById: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId), {\n            withCredentials: true\n        }),\n    // Domain Management - Nameservers\n    updateNameservers: (domainId, nameservers)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/nameservers\"), {\n            nameservers\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Auto Renewal\n    toggleAutoRenewal: (domainId, autoRenew)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/auto-renew\"), {\n            autoRenew\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Privacy Protection\n    togglePrivacyProtection: (domainId, privacyEnabled)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/privacy\"), {\n            privacyEnabled\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - DNS Records\n    getDnsRecords: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            withCredentials: true\n        }),\n    addDnsRecord: (domainId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    updateDnsRecord: (domainId, recordId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    deleteDnsRecord: (domainId, recordId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (domainMngService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2VydmljZXMvZG9tYWluTW5nU2VydmljZS5qcyIsIm1hcHBpbmdzIjoiOztBQUEyQztBQUUzQyxNQUFNQyxtQkFBbUI7SUFDdkIsc0JBQXNCO0lBQ3RCQyxnQkFBZ0IsQ0FBQ0MsT0FDZkgsdURBQVVBLENBQUNJLElBQUksQ0FBQyw4QkFBOEJELE1BQU07WUFDbERFLGlCQUFpQjtRQUNuQjtJQUVGLG9CQUFvQjtJQUNwQkMseUJBQXlCLENBQUNDLFNBQ3hCUCx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDJCQUEyQjtZQUN4Q0Q7WUFDQUYsaUJBQWlCO1FBQ25CO0lBRUZJLDRCQUE0QixDQUFDRixTQUMzQlAsdURBQVVBLENBQUNRLEdBQUcsQ0FBQywrQkFBK0I7WUFDNUNEO1lBQ0FGLGlCQUFpQjtRQUNuQjtJQUVGSyxnQ0FBZ0MsQ0FBQ0gsU0FDL0JQLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsbUNBQW1DO1lBQ2hERDtZQUNBRixpQkFBaUI7UUFDbkI7SUFFRk0sb0JBQW9CLENBQUNKLFNBQ25CUCx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDRCQUE0QjtZQUN6Q0Q7WUFDQUYsaUJBQWlCO1FBQ25CO0lBRUYsc0JBQXNCO0lBQ3RCTyxjQUFjLENBQUNMLFNBQ2JQLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsNkJBQTZCO1lBQzFDRDtZQUNBRixpQkFBaUI7UUFDbkI7SUFFRix1QkFBdUI7SUFDdkJRLG9CQUFvQixDQUFDTixTQUNuQlAsdURBQVVBLENBQUNRLEdBQUcsQ0FBQyxtQ0FBbUM7WUFDaEREO1lBQ0FGLGlCQUFpQjtRQUNuQjtJQUVGLHFCQUFxQjtJQUNyQlMsaUJBQWlCLENBQUNYLE9BQ2hCSCx1REFBVUEsQ0FBQ0ksSUFBSSxDQUFDLGlDQUFpQ0QsTUFBTTtZQUNyREUsaUJBQWlCO1FBQ25CO0lBRUYsMENBQTBDO0lBQzFDLG9EQUFvRDtJQUNwRFUsZUFBZSxDQUFDUixTQUNkUCx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDZCQUE2QjtZQUMxQ0Q7WUFDQUYsaUJBQWlCO1FBQ25CO0lBRUYsbUNBQW1DO0lBQ25DVyxnQkFBZ0IsSUFDZGhCLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsNEJBQTRCO1lBQ3pDSCxpQkFBaUI7UUFDbkI7SUFFRixxQkFBcUI7SUFDckJZLGtCQUFrQixDQUFDQyxhQUNqQmxCLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsNkJBQTZCO1lBQzFDRCxRQUFRO2dCQUFFVztZQUFXO1lBQ3JCYixpQkFBaUI7UUFDbkI7SUFFRix3REFBd0Q7SUFDeERjLHdCQUF3QixTQUFDRDtZQUFZRSwyRUFBVTtlQUM3Q3BCLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMscUNBQXFDO1lBQ2xERCxRQUFRO2dCQUFFVztnQkFBWUU7WUFBUTtZQUM5QmYsaUJBQWlCO1FBQ25COztJQUVGLGVBQWU7SUFDZmdCLGFBQWEsQ0FBQ2xCLE9BQ1pILHVEQUFVQSxDQUFDSSxJQUFJLENBQUMsMkJBQTJCRCxNQUFNO1lBQy9DRSxpQkFBaUI7UUFDbkI7SUFFRixzQkFBc0I7SUFDdEJpQixrQkFBa0IsQ0FBQ0o7UUFDakJLLFFBQVFDLEdBQUcsQ0FDVCxpRUFDQU47UUFFRkssUUFBUUMsR0FBRyxDQUFDLGtEQUF3QztZQUFFTjtRQUFXO1FBQ2pFLE9BQU9sQix1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDhCQUE4QjtZQUNsREQsUUFBUTtnQkFBRVc7WUFBVztZQUNyQmIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxtQ0FBbUM7SUFDbkNvQiwrQkFBK0IsQ0FBQ0MsYUFDOUIxQix1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDJDQUEyQztZQUN4REQsUUFBUW1CLGFBQWE7Z0JBQUVBO1lBQVcsSUFBSSxDQUFDO1lBQ3ZDckIsaUJBQWlCO1FBQ25CO0lBRUYscUJBQXFCO0lBQ3JCc0IsbUJBQW1CLENBQUN4QixPQUNsQkgsdURBQVVBLENBQUNJLElBQUksQ0FBQyxpQ0FBaUNELE1BQU07WUFDckRFLGlCQUFpQjtRQUNuQjtJQUVGLDRCQUE0QjtJQUM1QnVCLHlCQUF5QixDQUFDekIsT0FDeEJILHVEQUFVQSxDQUFDSSxJQUFJLENBQUMsNkJBQTZCRCxNQUFNO1lBQ2pERSxpQkFBaUI7UUFDbkI7SUFFRiw2QkFBNkI7SUFDN0J3QiwwQkFBMEIsQ0FBQzFCLE9BQ3pCSCx1REFBVUEsQ0FBQ0ksSUFBSSxDQUFDLDhCQUE4QkQsTUFBTTtZQUNsREUsaUJBQWlCO1FBQ25CO0lBRUZ5QixlQUFlLENBQUNDLFdBQ2QvQix1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLHNCQUErQixPQUFUdUIsV0FBWTtZQUMvQzFCLGlCQUFpQjtRQUNuQjtJQUVGLGtDQUFrQztJQUNsQzJCLG1CQUFtQixDQUFDRCxVQUFVRSxjQUM1QmpDLHVEQUFVQSxDQUFDa0MsR0FBRyxDQUNaLHNCQUErQixPQUFUSCxVQUFTLGlCQUMvQjtZQUFFRTtRQUFZLEdBQ2Q7WUFBRTVCLGlCQUFpQjtRQUFLO0lBRzVCLG1DQUFtQztJQUNuQzhCLG1CQUFtQixDQUFDSixVQUFVSyxZQUM1QnBDLHVEQUFVQSxDQUFDa0MsR0FBRyxDQUNaLHNCQUErQixPQUFUSCxVQUFTLGdCQUMvQjtZQUFFSztRQUFVLEdBQ1o7WUFBRS9CLGlCQUFpQjtRQUFLO0lBRzVCLHlDQUF5QztJQUN6Q2dDLHlCQUF5QixDQUFDTixVQUFVTyxpQkFDbEN0Qyx1REFBVUEsQ0FBQ2tDLEdBQUcsQ0FDWixzQkFBK0IsT0FBVEgsVUFBUyxhQUMvQjtZQUFFTztRQUFlLEdBQ2pCO1lBQUVqQyxpQkFBaUI7UUFBSztJQUc1QixrQ0FBa0M7SUFDbENrQyxlQUFlLENBQUNSLFdBQ2QvQix1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLHNCQUErQixPQUFUdUIsVUFBUyxTQUFPO1lBQ25EMUIsaUJBQWlCO1FBQ25CO0lBRUZtQyxjQUFjLENBQUNULFVBQVVVLFNBQ3ZCekMsdURBQVVBLENBQUNJLElBQUksQ0FDYixzQkFBK0IsT0FBVDJCLFVBQVMsU0FDL0I7WUFBRVU7UUFBTyxHQUNUO1lBQUVwQyxpQkFBaUI7UUFBSztJQUc1QnFDLGlCQUFpQixDQUFDWCxVQUFVWSxVQUFVRixTQUNwQ3pDLHVEQUFVQSxDQUFDa0MsR0FBRyxDQUNaLHNCQUFzQ1MsT0FBaEJaLFVBQVMsU0FBZ0IsT0FBVFksV0FDdEM7WUFBRUY7UUFBTyxHQUNUO1lBQUVwQyxpQkFBaUI7UUFBSztJQUc1QnVDLGlCQUFpQixDQUFDYixVQUFVWSxXQUMxQjNDLHVEQUFVQSxDQUFDNkMsTUFBTSxDQUFDLHNCQUFzQ0YsT0FBaEJaLFVBQVMsU0FBZ0IsT0FBVFksV0FBWTtZQUNsRXRDLGlCQUFpQjtRQUNuQjtBQUNKO0FBRUEsK0RBQWVKLGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3NlcnZpY2VzL2RvbWFpbk1uZ1NlcnZpY2UuanM/NDU0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXBpU2VydmljZSBmcm9tIFwiLi4vbGliL2FwaVNlcnZpY2VcIjtcclxuXHJcbmNvbnN0IGRvbWFpbk1uZ1NlcnZpY2UgPSB7XHJcbiAgLy8gQ3VzdG9tZXIgbWFuYWdlbWVudFxyXG4gIGN1c3RvbWVyU2lnbnVwOiAoZGF0YSkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcIi9kb21haW5NbmcvY3VzdG9tZXItc2lnbnVwXCIsIGRhdGEsIHtcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIERvbWFpbiBvcGVyYXRpb25zXHJcbiAgY2hlY2tEb21haW5BdmFpbGFiaWxpdHk6IChwYXJhbXMpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvY2hlY2stZG9tYWluXCIsIHtcclxuICAgICAgcGFyYW1zLFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgY2hlY2tJZG5Eb21haW5BdmFpbGFiaWxpdHk6IChwYXJhbXMpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvY2hlY2staWRuLWRvbWFpblwiLCB7XHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIGNoZWNrUHJlbWl1bURvbWFpbkF2YWlsYWJpbGl0eTogKHBhcmFtcykgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KFwiL2RvbWFpbk1uZy9jaGVjay1wcmVtaXVtLWRvbWFpblwiLCB7XHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIHN1Z2dlc3REb21haW5OYW1lczogKHBhcmFtcykgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KFwiL2RvbWFpbk1uZy9zdWdnZXN0LW5hbWVzXCIsIHtcclxuICAgICAgcGFyYW1zLFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gUHJpY2luZyBpbmZvcm1hdGlvblxyXG4gIGdldEROUHJpY2luZzogKHBhcmFtcykgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KFwiL2RvbWFpbk1uZy9nZXQtZG4tcHJpY2luZ1wiLCB7XHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIEdldCByZXNlbGxlciBwcmljaW5nXHJcbiAgZ2V0UmVzZWxsZXJQcmljaW5nOiAocGFyYW1zKSA9PlxyXG4gICAgYXBpU2VydmljZS5nZXQoXCIvZG9tYWluTW5nL2dldC1yZXNlbGxlci1wcmljaW5nXCIsIHtcclxuICAgICAgcGFyYW1zLFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gQWRkIGRvbWFpbiB0byBjYXJ0XHJcbiAgYWRkRG9tYWluVG9DYXJ0OiAoZGF0YSkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcIi9kb21haW5NbmcvYWRkLWRvbWFpbi10by1jYXJ0XCIsIGRhdGEsIHtcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIEFkZCB0aGUgbmV3IGNvbXByZWhlbnNpdmUgc2VhcmNoIG1ldGhvZFxyXG4gIC8vIEV4cGVjdGVkIHBhcmFtczogeyBwYXJhbXM6IFwiZG9tYWluLW5hbWUtc3RyaW5nXCIgfVxyXG4gIHNlYXJjaERvbWFpbnM6IChwYXJhbXMpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5Nbmcvc2VhcmNoLWRvbWFpbnNcIiwge1xyXG4gICAgICBwYXJhbXMsXHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBEb21haW4gTWFuYWdlbWVudCAtIFVzZXIgRG9tYWluc1xyXG4gIGdldFVzZXJEb21haW5zOiAoKSA9PlxyXG4gICAgYXBpU2VydmljZS5nZXQoXCIvb3JkZXIvZ2V0LWRvbWFpbi1vcmRlcnNcIiwge1xyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gR2V0IGRvbWFpbiBkZXRhaWxzXHJcbiAgZ2V0RG9tYWluRGV0YWlsczogKGRvbWFpbk5hbWUpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvZG9tYWluLWRldGFpbHNcIiwge1xyXG4gICAgICBwYXJhbXM6IHsgZG9tYWluTmFtZSB9LFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gR2V0IGRvbWFpbiBkZXRhaWxzIGJ5IG5hbWUgKGZyb20gcmVnaXN0cmF0aW9uIHN5c3RlbSlcclxuICBnZXREb21haW5EZXRhaWxzQnlOYW1lOiAoZG9tYWluTmFtZSwgb3B0aW9ucyA9IFwiT3JkZXJEZXRhaWxzXCIpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvZG9tYWluLWRldGFpbHMtYnktbmFtZVwiLCB7XHJcbiAgICAgIHBhcmFtczogeyBkb21haW5OYW1lLCBvcHRpb25zIH0sXHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBSZW5ldyBkb21haW5cclxuICByZW5ld0RvbWFpbjogKGRhdGEpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXCIvZG9tYWluTW5nL3JlbmV3LWRvbWFpblwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBHZXQgZG9tYWluIG9yZGVyIElEXHJcbiAgZ2V0RG9tYWluT3JkZXJJZDogKGRvbWFpbk5hbWUpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICBcIvCflKcgRnJvbnRlbmQgc2VydmljZSAtIGdldERvbWFpbk9yZGVySWQgY2FsbGVkIHdpdGg6XCIsXHJcbiAgICAgIGRvbWFpbk5hbWVcclxuICAgICk7XHJcbiAgICBjb25zb2xlLmxvZyhcIvCflKcgRnJvbnRlbmQgc2VydmljZSAtIHBhcmFtcyBvYmplY3Q6XCIsIHsgZG9tYWluTmFtZSB9KTtcclxuICAgIHJldHVybiBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvZG9tYWluLW9yZGVyLWlkXCIsIHtcclxuICAgICAgcGFyYW1zOiB7IGRvbWFpbk5hbWUgfSxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IGN1c3RvbWVyIGRlZmF1bHQgbmFtZXNlcnZlcnNcclxuICBnZXRDdXN0b21lckRlZmF1bHROYW1lc2VydmVyczogKGN1c3RvbWVySWQpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvY3VzdG9tZXItZGVmYXVsdC1uYW1lc2VydmVyc1wiLCB7XHJcbiAgICAgIHBhcmFtczogY3VzdG9tZXJJZCA/IHsgY3VzdG9tZXJJZCB9IDoge30sXHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBNb2RpZnkgbmFtZXNlcnZlcnNcclxuICBtb2RpZnlOYW1lc2VydmVyczogKGRhdGEpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXCIvZG9tYWluTW5nL21vZGlmeS1uYW1lc2VydmVyc1wiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBFbmFibGUgcHJpdmFjeSBwcm90ZWN0aW9uXHJcbiAgZW5hYmxlUHJpdmFjeVByb3RlY3Rpb246IChkYXRhKSA9PlxyXG4gICAgYXBpU2VydmljZS5wb3N0KFwiL2RvbWFpbk1uZy9lbmFibGUtcHJpdmFjeVwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBEaXNhYmxlIHByaXZhY3kgcHJvdGVjdGlvblxyXG4gIGRpc2FibGVQcml2YWN5UHJvdGVjdGlvbjogKGRhdGEpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXCIvZG9tYWluTW5nL2Rpc2FibGUtcHJpdmFjeVwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICBnZXREb21haW5CeUlkOiAoZG9tYWluSWQpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChgL2RvbWFpbk1uZy9kb21haW5zLyR7ZG9tYWluSWR9YCwge1xyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gRG9tYWluIE1hbmFnZW1lbnQgLSBOYW1lc2VydmVyc1xyXG4gIHVwZGF0ZU5hbWVzZXJ2ZXJzOiAoZG9tYWluSWQsIG5hbWVzZXJ2ZXJzKSA9PlxyXG4gICAgYXBpU2VydmljZS5wdXQoXHJcbiAgICAgIGAvZG9tYWluTW5nL2RvbWFpbnMvJHtkb21haW5JZH0vbmFtZXNlcnZlcnNgLFxyXG4gICAgICB7IG5hbWVzZXJ2ZXJzIH0sXHJcbiAgICAgIHsgd2l0aENyZWRlbnRpYWxzOiB0cnVlIH1cclxuICAgICksXHJcblxyXG4gIC8vIERvbWFpbiBNYW5hZ2VtZW50IC0gQXV0byBSZW5ld2FsXHJcbiAgdG9nZ2xlQXV0b1JlbmV3YWw6IChkb21haW5JZCwgYXV0b1JlbmV3KSA9PlxyXG4gICAgYXBpU2VydmljZS5wdXQoXHJcbiAgICAgIGAvZG9tYWluTW5nL2RvbWFpbnMvJHtkb21haW5JZH0vYXV0by1yZW5ld2AsXHJcbiAgICAgIHsgYXV0b1JlbmV3IH0sXHJcbiAgICAgIHsgd2l0aENyZWRlbnRpYWxzOiB0cnVlIH1cclxuICAgICksXHJcblxyXG4gIC8vIERvbWFpbiBNYW5hZ2VtZW50IC0gUHJpdmFjeSBQcm90ZWN0aW9uXHJcbiAgdG9nZ2xlUHJpdmFjeVByb3RlY3Rpb246IChkb21haW5JZCwgcHJpdmFjeUVuYWJsZWQpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnB1dChcclxuICAgICAgYC9kb21haW5NbmcvZG9tYWlucy8ke2RvbWFpbklkfS9wcml2YWN5YCxcclxuICAgICAgeyBwcml2YWN5RW5hYmxlZCB9LFxyXG4gICAgICB7IHdpdGhDcmVkZW50aWFsczogdHJ1ZSB9XHJcbiAgICApLFxyXG5cclxuICAvLyBEb21haW4gTWFuYWdlbWVudCAtIEROUyBSZWNvcmRzXHJcbiAgZ2V0RG5zUmVjb3JkczogKGRvbWFpbklkKSA9PlxyXG4gICAgYXBpU2VydmljZS5nZXQoYC9kb21haW5NbmcvZG9tYWlucy8ke2RvbWFpbklkfS9kbnNgLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICBhZGREbnNSZWNvcmQ6IChkb21haW5JZCwgcmVjb3JkKSA9PlxyXG4gICAgYXBpU2VydmljZS5wb3N0KFxyXG4gICAgICBgL2RvbWFpbk1uZy9kb21haW5zLyR7ZG9tYWluSWR9L2Ruc2AsXHJcbiAgICAgIHsgcmVjb3JkIH0sXHJcbiAgICAgIHsgd2l0aENyZWRlbnRpYWxzOiB0cnVlIH1cclxuICAgICksXHJcblxyXG4gIHVwZGF0ZURuc1JlY29yZDogKGRvbWFpbklkLCByZWNvcmRJZCwgcmVjb3JkKSA9PlxyXG4gICAgYXBpU2VydmljZS5wdXQoXHJcbiAgICAgIGAvZG9tYWluTW5nL2RvbWFpbnMvJHtkb21haW5JZH0vZG5zLyR7cmVjb3JkSWR9YCxcclxuICAgICAgeyByZWNvcmQgfSxcclxuICAgICAgeyB3aXRoQ3JlZGVudGlhbHM6IHRydWUgfVxyXG4gICAgKSxcclxuXHJcbiAgZGVsZXRlRG5zUmVjb3JkOiAoZG9tYWluSWQsIHJlY29yZElkKSA9PlxyXG4gICAgYXBpU2VydmljZS5kZWxldGUoYC9kb21haW5NbmcvZG9tYWlucy8ke2RvbWFpbklkfS9kbnMvJHtyZWNvcmRJZH1gLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZG9tYWluTW5nU2VydmljZTtcclxuIl0sIm5hbWVzIjpbImFwaVNlcnZpY2UiLCJkb21haW5NbmdTZXJ2aWNlIiwiY3VzdG9tZXJTaWdudXAiLCJkYXRhIiwicG9zdCIsIndpdGhDcmVkZW50aWFscyIsImNoZWNrRG9tYWluQXZhaWxhYmlsaXR5IiwicGFyYW1zIiwiZ2V0IiwiY2hlY2tJZG5Eb21haW5BdmFpbGFiaWxpdHkiLCJjaGVja1ByZW1pdW1Eb21haW5BdmFpbGFiaWxpdHkiLCJzdWdnZXN0RG9tYWluTmFtZXMiLCJnZXRETlByaWNpbmciLCJnZXRSZXNlbGxlclByaWNpbmciLCJhZGREb21haW5Ub0NhcnQiLCJzZWFyY2hEb21haW5zIiwiZ2V0VXNlckRvbWFpbnMiLCJnZXREb21haW5EZXRhaWxzIiwiZG9tYWluTmFtZSIsImdldERvbWFpbkRldGFpbHNCeU5hbWUiLCJvcHRpb25zIiwicmVuZXdEb21haW4iLCJnZXREb21haW5PcmRlcklkIiwiY29uc29sZSIsImxvZyIsImdldEN1c3RvbWVyRGVmYXVsdE5hbWVzZXJ2ZXJzIiwiY3VzdG9tZXJJZCIsIm1vZGlmeU5hbWVzZXJ2ZXJzIiwiZW5hYmxlUHJpdmFjeVByb3RlY3Rpb24iLCJkaXNhYmxlUHJpdmFjeVByb3RlY3Rpb24iLCJnZXREb21haW5CeUlkIiwiZG9tYWluSWQiLCJ1cGRhdGVOYW1lc2VydmVycyIsIm5hbWVzZXJ2ZXJzIiwicHV0IiwidG9nZ2xlQXV0b1JlbmV3YWwiLCJhdXRvUmVuZXciLCJ0b2dnbGVQcml2YWN5UHJvdGVjdGlvbiIsInByaXZhY3lFbmFibGVkIiwiZ2V0RG5zUmVjb3JkcyIsImFkZERuc1JlY29yZCIsInJlY29yZCIsInVwZGF0ZURuc1JlY29yZCIsInJlY29yZElkIiwiZGVsZXRlRG5zUmVjb3JkIiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/domainMngService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/external-link.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ExternalLink; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 3h6v6\",\n            key: \"1q9fwt\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 14 21 3\",\n            key: \"gplh6r\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\",\n            key: \"a6xqqp\"\n        }\n    ]\n];\nconst ExternalLink = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ExternalLink\", __iconNode);\n //# sourceMappingURL=external-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZXh0ZXJuYWwtbGluay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQWFDLEtBQUs7UUFBUztLQUFFO0lBQzNDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQWVDLEtBQUs7UUFBUztLQUFFO0lBQzdDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQTREQyxLQUFLO1FBQVM7S0FBRTtDQUMzRjtBQUNELE1BQU1DLGVBQWVKLGdFQUFnQkEsQ0FBQyxnQkFBZ0JDO0FBRVAsQ0FDL0MseUNBQXlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZXh0ZXJuYWwtbGluay5qcz9mYjllIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjQ3NS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgX19pY29uTm9kZSA9IFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE1IDNoNnY2XCIsIGtleTogXCIxcTlmd3RcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTEwIDE0IDIxIDNcIiwga2V5OiBcImdwbGg2clwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMTggMTN2NmEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY4YTIgMiAwIDAgMSAyLTJoNlwiLCBrZXk6IFwiYTZ4cXFwXCIgfV1cbl07XG5jb25zdCBFeHRlcm5hbExpbmsgPSBjcmVhdGVMdWNpZGVJY29uKFwiRXh0ZXJuYWxMaW5rXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBFeHRlcm5hbExpbmsgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXh0ZXJuYWwtbGluay5qcy5tYXBcbiJdLCJuYW1lcyI6WyJjcmVhdGVMdWNpZGVJY29uIiwiX19pY29uTm9kZSIsImQiLCJrZXkiLCJFeHRlcm5hbExpbmsiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/lock.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Lock; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"11\",\n            x: \"3\",\n            y: \"11\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1w4ew1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 11V7a5 5 0 0 1 10 0v4\",\n            key: \"fwvmzm\"\n        }\n    ]\n];\nconst Lock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Lock\", __iconNode);\n //# sourceMappingURL=lock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbG9jay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxPQUFPO1lBQU1DLFFBQVE7WUFBTUMsR0FBRztZQUFLQyxHQUFHO1lBQU1DLElBQUk7WUFBS0MsSUFBSTtZQUFLQyxLQUFLO1FBQVM7S0FBRTtJQUN6RjtRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUE0QkQsS0FBSztRQUFTO0tBQUU7Q0FDM0Q7QUFDRCxNQUFNRSxPQUFPVixnRUFBZ0JBLENBQUMsUUFBUUM7QUFFQyxDQUN2QyxnQ0FBZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9sb2NrLmpzP2U5MjYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXCJyZWN0XCIsIHsgd2lkdGg6IFwiMThcIiwgaGVpZ2h0OiBcIjExXCIsIHg6IFwiM1wiLCB5OiBcIjExXCIsIHJ4OiBcIjJcIiwgcnk6IFwiMlwiLCBrZXk6IFwiMXc0ZXcxXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk03IDExVjdhNSA1IDAgMCAxIDEwIDB2NFwiLCBrZXk6IFwiZnd2bXptXCIgfV1cbl07XG5jb25zdCBMb2NrID0gY3JlYXRlTHVjaWRlSWNvbihcIkxvY2tcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIExvY2sgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9jay5qcy5tYXBcbiJdLCJuYW1lcyI6WyJjcmVhdGVMdWNpZGVJY29uIiwiX19pY29uTm9kZSIsIndpZHRoIiwiaGVpZ2h0IiwieCIsInkiLCJyeCIsInJ5Iiwia2V5IiwiZCIsIkxvY2siLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/server.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Server; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"ngkwjq\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"14\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"iecqi9\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"16zg32\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"nzw8ys\"\n        }\n    ]\n];\nconst Server = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Server\", __iconNode);\n //# sourceMappingURL=server.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/shield.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Shield; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\",\n            key: \"oel41y\"\n        }\n    ]\n];\nconst Shield = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Shield\", __iconNode);\n //# sourceMappingURL=shield.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2hpZWxkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7OztDQUtDLEdBRXFEO0FBRXRELE1BQU1DLGFBQWE7SUFDakI7UUFDRTtRQUNBO1lBQ0VDLEdBQUc7WUFDSEMsS0FBSztRQUNQO0tBQ0Q7Q0FDRjtBQUNELE1BQU1DLFNBQVNKLGdFQUFnQkEsQ0FBQyxVQUFVQztBQUVELENBQ3pDLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3NoaWVsZC5qcz9kMjY4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjQ3NS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgX19pY29uTm9kZSA9IFtcbiAgW1xuICAgIFwicGF0aFwiLFxuICAgIHtcbiAgICAgIGQ6IFwiTTIwIDEzYzAgNS0zLjUgNy41LTcuNjYgOC45NWExIDEgMCAwIDEtLjY3LS4wMUM3LjUgMjAuNSA0IDE4IDQgMTNWNmExIDEgMCAwIDEgMS0xYzIgMCA0LjUtMS4yIDYuMjQtMi43MmExLjE3IDEuMTcgMCAwIDEgMS41MiAwQzE0LjUxIDMuODEgMTcgNSAxOSA1YTEgMSAwIDAgMSAxIDF6XCIsXG4gICAgICBrZXk6IFwib2VsNDF5XCJcbiAgICB9XG4gIF1cbl07XG5jb25zdCBTaGllbGQgPSBjcmVhdGVMdWNpZGVJY29uKFwiU2hpZWxkXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBTaGllbGQgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2hpZWxkLmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJfX2ljb25Ob2RlIiwiZCIsImtleSIsIlNoaWVsZCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\n"));

/***/ })

});