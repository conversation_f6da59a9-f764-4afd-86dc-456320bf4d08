"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const [privacyProtection, setPrivacyProtection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Privacy protection option\n    const [autoRenew, setAutoRenew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Auto-renewal option\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period,\n                privacyProtection: privacyProtection,\n                autoRenew: autoRenew\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-gray-700 mb-3\",\n                            children: t ? t(\"domain.options\") : \"Domain Options\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"privacy-protection\",\n                                            checked: privacyProtection,\n                                            onChange: (e)=>setPrivacyProtection(e.target.checked),\n                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"privacy-protection\",\n                                            className: \"text-sm text-gray-600\",\n                                            children: t ? t(\"domain.privacy_protection\") : \"Privacy Protection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"auto-renewal\",\n                                            checked: autoRenew,\n                                            onChange: (e)=>setAutoRenew(e.target.checked),\n                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"auto-renewal\",\n                                            className: \"text-sm text-gray-600\",\n                                            children: t ? t(\"domain.auto_renewal\") : \"Auto Renewal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 260,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 271,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 264,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"0dlu9g0/oDsgZKGxTTSKKJ9GQXc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});