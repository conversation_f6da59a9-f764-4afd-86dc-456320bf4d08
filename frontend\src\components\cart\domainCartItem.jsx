import React, { useState, useEffect } from "react";
import { TrashIcon, Globe, Users } from "lucide-react";
import { toast } from "react-toastify";
import cartService from "@/app/services/cartService";
import DomainContactModal from "./domainContactModal";
import { useAuth } from "@/app/context/AuthContext";

// A small wrapper for the icon container.
const IconWrapper = ({ children }) => (
  <div className="h-12 w-12 flex-shrink-0 bg-blue-100 rounded-lg flex items-center justify-center">
    {children}
  </div>
);

function DomainCartItem({
  item,
  onPeriodChange,
  onRemove,
  t,
  onPrivacyChange,
}) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [period, setPeriod] = useState(item.period || 1);
  const [isPeriodChanging, setIsPeriodChanging] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [localPrivacyProtection, setLocalPrivacyProtection] = useState(
    item.privacyProtection || false
  );
  const { user } = useAuth();

  // Sync local period state with item data when it changes
  useEffect(() => {
    setPeriod(item.period || 1);
  }, [item.period]);

  // Handle privacy protection change
  const handlePrivacyToggle = (checked) => {
    setLocalPrivacyProtection(checked);
    if (onPrivacyChange) {
      onPrivacyChange(item._id, checked);
    }
  };

  // Get available periods from raw pricing data
  const getAvailablePeriods = () => {
    if (
      item.rawPricing &&
      typeof item.rawPricing === "object" &&
      item.rawPricing.addnewdomain &&
      typeof item.rawPricing.addnewdomain === "object"
    ) {
      const periods = Object.keys(item.rawPricing.addnewdomain)
        .map((p) => parseInt(p))
        .filter((p) => !isNaN(p) && p > 0)
        .sort((a, b) => a - b);

      // Return periods if we found any, otherwise fallback
      return periods.length > 0 ? periods : [1, 2, 3, 5, 10];
    }
    // Fallback to default periods if no raw pricing data
    return [1, 2, 3, 5, 10];
  };

  const availablePeriods = getAvailablePeriods();

  // Get price for a specific period
  const getPriceForPeriod = (periodValue) => {
    if (
      item.rawPricing &&
      typeof item.rawPricing === "object" &&
      item.rawPricing.addnewdomain &&
      typeof item.rawPricing.addnewdomain === "object"
    ) {
      const pricePerYear = item.rawPricing.addnewdomain[periodValue.toString()];
      if (pricePerYear && !isNaN(parseFloat(pricePerYear))) {
        // For domains, total price = price per year * period
        return parseFloat(pricePerYear) * periodValue;
      }
    }
    // Fallback to current item price
    return item.price || 0;
  };

  const handlePeriodChange = async (e) => {
    const periodNum = parseInt(e.target.value, 10);

    console.log("Domain period change:", {
      domainName: item.domainName,
      oldPeriod: period,
      newPeriod: periodNum,
      itemId: item._id,
      currentPrice: item.price,
      newPrice: getPriceForPeriod(periodNum),
    });

    try {
      setIsPeriodChanging(true);
      setPeriod(periodNum); // Update local state immediately for better UX

      // Call the parent's period change handler
      await onPeriodChange(item._id, periodNum, true);

      console.log("Period change successful");
      toast.success(t ? t("period_updated") : "Period updated successfully");
    } catch (error) {
      console.error("Error updating period:", error);
      // Revert local state on error
      setPeriod(item.period || 1);
      toast.error(t ? t("error_updating_period") : "Error updating period");
    } finally {
      setIsPeriodChanging(false);
    }
  };

  const handleRemoveItem = async () => {
    try {
      setIsUpdating(true);
      await cartService.removeDomainFromCart({ itemId: item._id });

      // Call the onRemove callback if provided, otherwise reload the page
      if (onRemove) {
        onRemove(item._id);
      } else {
        window.location.reload();
      }

      toast.success(
        t ? t("domain_removed_from_cart") : "Domain removed from cart"
      );
    } catch (error) {
      console.error("Error removing domain from cart:", error);
      toast.error(
        t ? t("error_removing_item") : "Error removing item from cart"
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="relative cart-item-container bg-white shadow-sm border border-gray-100 sm:mt-3 pb-4 mb-4 rounded-lg">
      <div className="flex flex-col sm:flex-row items-center py-2 px-2 w-full">
        <div className="flex flex-col sm:flex-row items-center justify-between flex-grow w-full mb-4 md:mb-0">
          <div className="flex flex-row items-center gap-4 w-full justify-center sm:justify-start">
            <IconWrapper>
              <Globe className="h-6 w-6 text-blue-600" />
            </IconWrapper>
            <div className="min-w-0 flex-grow text-left">
              <p className="font-medium text-sm text-gray-800">
                {item.domainName}
              </p>
              <p className="text-sm text-gray-600">
                {t ? t("domainWrapper.registration") : "Domain Registration"}
              </p>
              <div className="flex flex-col">
                <p className="text-sm font-medium text-gray-800">
                  {t ? t("total") : "Total"}:{" "}
                  {getPriceForPeriod(period).toFixed(2)} MAD
                </p>
              </div>

              {/* Contact Management Button */}
              {user && user.email && (
                <div className="mt-2">
                  <button
                    onClick={() => setShowContactModal(true)}
                    className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    <Users className="h-3 w-3" />
                    <span>
                      {t
                        ? t("domainWrapper.manage_contacts")
                        : "Manage Contacts"}
                    </span>
                  </button>
                </div>
              )}

              {/* Domain Options */}
              <div className="mt-4 border-t border-gray-200 pt-3">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-800 flex items-center">
                    <svg
                      className="w-4 h-4 mr-2 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                      />
                    </svg>
                    {t ? t("domain.options") : "Options de Domaine"}
                  </h4>
                </div>

                {/* ID Protect Option */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id={`privacy-${item._id}`}
                      checked={localPrivacyProtection}
                      onChange={(e) => handlePrivacyToggle(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <label
                          htmlFor={`privacy-${item._id}`}
                          className="text-sm font-medium text-blue-900 cursor-pointer"
                        >
                          {t ? t("domain.id_protect") : "ID Protect"}
                        </label>
                        <span className="text-sm font-semibold text-blue-700">
                          {t ? t("domain.id_protect_price") : "39.00 DH HT/an"}
                        </span>
                      </div>
                      <p className="text-xs text-blue-700 mt-1">
                        {t
                          ? t("domain.id_protect_short_desc")
                          : "Protégez vos données personnelles dans le WHOIS"}
                      </p>
                      <button
                        type="button"
                        className="text-xs text-blue-600 hover:text-blue-800 underline mt-1"
                        onClick={() =>
                          window.open("/domains/id-protect", "_blank")
                        }
                      >
                        {t
                          ? t("domain.see_more_details")
                          : "Voir plus de détails"}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-center gap-[35px] mt-4 md:mt-0 self-start sm:self-auto">
            {/* Period selector for domains */}
            <div className="flex sm:flex-col items-start mt-2 flex-row md:mt-0 md:mr-5">
              <label
                htmlFor="period"
                className="block text-sm font-medium w-full text-left text-gray-700 mr-2"
              >
                {t ? t("period") : "Period"}
                {isPeriodChanging && (
                  <span className="ml-1 text-xs text-blue-500">
                    (updating...)
                  </span>
                )}
              </label>
              <select
                id="period"
                value={period}
                onChange={handlePeriodChange}
                disabled={isPeriodChanging || isUpdating}
                className={`text-sm lg:w-[150px] rounded-md border border-gray-300 py-1.5 px-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${
                  isPeriodChanging || isUpdating
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                {availablePeriods.map((periodOption) => (
                  <option key={periodOption} value={periodOption}>
                    {periodOption}{" "}
                    {periodOption === 1
                      ? t
                        ? t("year2")
                        : "year"
                      : t
                      ? t("years2")
                      : "years"}
                  </option>
                ))}
              </select>
            </div>

            {/* Remove button */}
            <button
              className="absolute sm:static top-[34%] right-2 text-sm sm:ml-5 text-red-500 flex items-center justify-center hover:bg-red-500 hover:bg-opacity-80 hover:text-white py-1 px-2 rounded-md mt-4 md:mt-0"
              onClick={handleRemoveItem}
              disabled={isUpdating}
            >
              <TrashIcon width={18} className="mx-auto" />
            </button>
          </div>
        </div>
      </div>

      {/* Contact Management Modal */}
      <DomainContactModal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
      />
    </div>
  );
}

export default DomainCartItem;
