import React, { useState, useEffect } from "react";
import { TrashIcon, Globe, Users } from "lucide-react";
import { toast } from "react-toastify";
import cartService from "@/app/services/cartService";
import DomainContactModal from "./domainContactModal";
import { useAuth } from "@/app/context/AuthContext";

// A small wrapper for the icon container.
const IconWrapper = ({ children }) => (
  <div className="h-12 w-12 flex-shrink-0 bg-blue-100 rounded-lg flex items-center justify-center">
    {children}
  </div>
);

function DomainCartItem({
  item,
  onPeriodChange,
  onRemove,
  t,
  onPrivacyChange,
}) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [period, setPeriod] = useState(item.period || 1);
  const [isPeriodChanging, setIsPeriodChanging] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [localPrivacyProtection, setLocalPrivacyProtection] = useState(
    item.privacyProtection || false
  );
  const { user } = useAuth();

  // Sync local period state with item data when it changes
  useEffect(() => {
    setPeriod(item.period || 1);
  }, [item.period]);

  // Handle privacy protection change
  const handlePrivacyToggle = (checked) => {
    setLocalPrivacyProtection(checked);
    if (onPrivacyChange) {
      onPrivacyChange(item._id, checked);
    }
  };

  // Get available periods from raw pricing data
  const getAvailablePeriods = () => {
    if (
      item.rawPricing &&
      typeof item.rawPricing === "object" &&
      item.rawPricing.addnewdomain &&
      typeof item.rawPricing.addnewdomain === "object"
    ) {
      const periods = Object.keys(item.rawPricing.addnewdomain)
        .map((p) => parseInt(p))
        .filter((p) => !isNaN(p) && p > 0)
        .sort((a, b) => a - b);

      // Return periods if we found any, otherwise fallback
      return periods.length > 0 ? periods : [1, 2, 3, 5, 10];
    }
    // Fallback to default periods if no raw pricing data
    return [1, 2, 3, 5, 10];
  };

  const availablePeriods = getAvailablePeriods();

  // Get price for a specific period
  const getPriceForPeriod = (periodValue) => {
    if (
      item.rawPricing &&
      typeof item.rawPricing === "object" &&
      item.rawPricing.addnewdomain &&
      typeof item.rawPricing.addnewdomain === "object"
    ) {
      const pricePerYear = item.rawPricing.addnewdomain[periodValue.toString()];
      if (pricePerYear && !isNaN(parseFloat(pricePerYear))) {
        // For domains, total price = price per year * period
        return parseFloat(pricePerYear) * periodValue;
      }
    }
    // Fallback to current item price
    return item.price || 0;
  };

  const handlePeriodChange = async (e) => {
    const periodNum = parseInt(e.target.value, 10);

    console.log("Domain period change:", {
      domainName: item.domainName,
      oldPeriod: period,
      newPeriod: periodNum,
      itemId: item._id,
      currentPrice: item.price,
      newPrice: getPriceForPeriod(periodNum),
    });

    try {
      setIsPeriodChanging(true);
      setPeriod(periodNum); // Update local state immediately for better UX

      // Call the parent's period change handler
      await onPeriodChange(item._id, periodNum, true);

      console.log("Period change successful");
      toast.success(t ? t("period_updated") : "Period updated successfully");
    } catch (error) {
      console.error("Error updating period:", error);
      // Revert local state on error
      setPeriod(item.period || 1);
      toast.error(t ? t("error_updating_period") : "Error updating period");
    } finally {
      setIsPeriodChanging(false);
    }
  };

  const handleRemoveItem = async () => {
    try {
      setIsUpdating(true);
      await cartService.removeDomainFromCart({ itemId: item._id });

      // Call the onRemove callback if provided, otherwise reload the page
      if (onRemove) {
        onRemove(item._id);
      } else {
        window.location.reload();
      }

      toast.success(
        t ? t("domain_removed_from_cart") : "Domain removed from cart"
      );
    } catch (error) {
      console.error("Error removing domain from cart:", error);
      toast.error(
        t ? t("error_removing_item") : "Error removing item from cart"
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="relative cart-item-container bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 shadow-lg hover:shadow-xl border border-blue-200/50 sm:mt-3 pb-6 mb-6 rounded-2xl transition-all duration-300 hover:scale-[1.02]">
      {/* Premium Badge */}
      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-md">
        DOMAIN
      </div>

      <div className="flex flex-col sm:flex-row items-center py-4 px-4 w-full">
        <div className="flex flex-col sm:flex-row items-center justify-between flex-grow w-full mb-4 md:mb-0">
          <div className="flex flex-row items-center gap-5 w-full justify-center sm:justify-start">
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 p-4 rounded-2xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                <Globe className="h-7 w-7 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 bg-emerald-500 w-4 h-4 rounded-full border-2 border-white shadow-sm animate-pulse"></div>
            </div>
            <div className="min-w-0 flex-grow text-left">
              <div className="flex items-center gap-2 mb-1">
                <p className="font-bold text-lg text-gray-900 tracking-wide">
                  {item.domainName}
                </p>
                <div className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full">
                  AVAILABLE
                </div>
              </div>
              <p className="text-sm text-blue-600 font-medium flex items-center gap-1">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                {t ? t("domainWrapper.registration") : "Domain Registration"}
              </p>
              <div className="bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-200 rounded-xl p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-1">
                  <svg
                    className="w-5 h-5 text-emerald-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                  <span className="text-sm font-semibold text-emerald-800">
                    {t ? t("total") : "Total"}
                  </span>
                </div>
                <div className="flex items-baseline gap-1">
                  <span className="text-2xl font-bold text-emerald-700">
                    {getPriceForPeriod(period).toFixed(2)}
                  </span>
                  <span className="text-sm font-medium text-emerald-600">
                    MAD
                  </span>
                </div>
                <div className="text-xs text-emerald-600 mt-1">
                  {period === 1 ? "pour 1 an" : `pour ${period} ans`}
                </div>
              </div>

              {/* Contact Management Button */}
              {user && user.email && (
                <div className="mt-2">
                  <button
                    onClick={() => setShowContactModal(true)}
                    className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    <Users className="h-3 w-3" />
                    <span>
                      {t
                        ? t("domainWrapper.manage_contacts")
                        : "Manage Contacts"}
                    </span>
                  </button>
                </div>
              )}

              {/* Domain Options */}
              <div className="mt-4 border-t border-gray-200 pt-3">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-800 flex items-center">
                    <svg
                      className="w-4 h-4 mr-2 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                      />
                    </svg>
                    {t ? t("domain.options") : "Options de Domaine"}
                  </h4>
                </div>

                {/* ID Protect Option */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id={`privacy-${item._id}`}
                      checked={localPrivacyProtection}
                      onChange={(e) => handlePrivacyToggle(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <label
                          htmlFor={`privacy-${item._id}`}
                          className="text-sm font-medium text-blue-900 cursor-pointer"
                        >
                          {t ? t("domain.id_protect") : "ID Protect"}
                        </label>
                        <span className="text-sm font-semibold text-blue-700">
                          {t ? t("domain.id_protect_price") : "39.00 DH HT/an"}
                        </span>
                      </div>
                      <p className="text-xs text-blue-700 mt-1">
                        {t
                          ? t("domain.id_protect_short_desc")
                          : "Protégez vos données personnelles dans le WHOIS"}
                      </p>
                      <button
                        type="button"
                        className="text-xs text-blue-600 hover:text-blue-800 underline mt-1"
                        onClick={() =>
                          window.open("/domains/id-protect", "_blank")
                        }
                      >
                        {t
                          ? t("domain.see_more_details")
                          : "Voir plus de détails"}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-center gap-6 mt-6 md:mt-0 self-start sm:self-auto">
            {/* Enhanced Period selector */}
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-blue-200/50 shadow-sm">
              <label
                htmlFor="period"
                className="flex items-center gap-2 text-sm font-semibold text-gray-800 mb-2"
              >
                <svg
                  className="w-4 h-4 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                {t ? t("period") : "Période"}
                {isPeriodChanging && (
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-xs text-blue-600 font-medium">
                      Mise à jour...
                    </span>
                  </div>
                )}
              </label>
              <select
                id="period"
                value={period}
                onChange={handlePeriodChange}
                disabled={isPeriodChanging || isUpdating}
                className={`w-full text-sm bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200 py-3 px-4 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 font-medium ${
                  isPeriodChanging || isUpdating
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:border-blue-400"
                }`}
              >
                {availablePeriods.map((periodOption) => (
                  <option key={periodOption} value={periodOption}>
                    {periodOption}{" "}
                    {periodOption === 1
                      ? t
                        ? t("year2")
                        : "year"
                      : t
                      ? t("years2")
                      : "years"}
                  </option>
                ))}
              </select>
            </div>

            {/* Enhanced Remove button */}
            <button
              className="group relative bg-gradient-to-r from-red-50 to-pink-50 hover:from-red-500 hover:to-pink-500 border-2 border-red-200 hover:border-red-500 text-red-600 hover:text-white rounded-xl p-3 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleRemoveItem}
              disabled={isUpdating}
              title={t ? t("remove_from_cart") : "Supprimer du panier"}
            >
              <TrashIcon
                width={20}
                className="mx-auto transition-transform duration-300 group-hover:scale-110"
              />
              {isUpdating && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
                  <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Contact Management Modal */}
      <DomainContactModal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
      />
    </div>
  );
}

export default DomainCartItem;
