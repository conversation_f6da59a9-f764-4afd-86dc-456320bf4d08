import React, { useState, useEffect } from "react";
import { TrashIcon, Globe, Users } from "lucide-react";
import { toast } from "react-toastify";
import cartService from "@/app/services/cartService";
import DomainContactModal from "./domainContactModal";
import { useAuth } from "@/app/context/AuthContext";

function DomainCartItem({
  item,
  onPeriodChange,
  onRemove,
  t,
  onPrivacyChange,
}) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [period, setPeriod] = useState(item.period || 1);
  const [isPeriodChanging, setIsPeriodChanging] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [localPrivacyProtection, setLocalPrivacyProtection] = useState(
    item.privacyProtection || false
  );
  const { user } = useAuth();

  // Sync local period state with item data when it changes
  useEffect(() => {
    setPeriod(item.period || 1);
  }, [item.period]);

  // Handle privacy protection change
  const handlePrivacyToggle = (checked) => {
    setLocalPrivacyProtection(checked);
    if (onPrivacyChange) {
      onPrivacyChange(item._id, checked);
    }
  };

  // Get available periods from raw pricing data
  const getAvailablePeriods = () => {
    if (
      item.rawPricing &&
      typeof item.rawPricing === "object" &&
      item.rawPricing.addnewdomain &&
      typeof item.rawPricing.addnewdomain === "object"
    ) {
      const periods = Object.keys(item.rawPricing.addnewdomain)
        .map((p) => parseInt(p))
        .filter((p) => !isNaN(p) && p > 0)
        .sort((a, b) => a - b);

      // Return periods if we found any, otherwise fallback
      return periods.length > 0 ? periods : [1, 2, 3, 5, 10];
    }
    // Fallback to default periods if no raw pricing data
    return [1, 2, 3, 5, 10];
  };

  const availablePeriods = getAvailablePeriods();

  // Get price for a specific period
  const getPriceForPeriod = (periodValue) => {
    if (
      item.rawPricing &&
      typeof item.rawPricing === "object" &&
      item.rawPricing.addnewdomain &&
      typeof item.rawPricing.addnewdomain === "object"
    ) {
      const pricePerYear = item.rawPricing.addnewdomain[periodValue.toString()];
      if (pricePerYear && !isNaN(parseFloat(pricePerYear))) {
        // For domains, total price = price per year * period
        return parseFloat(pricePerYear) * periodValue;
      }
    }
    // Fallback to current item price
    return item.price || 0;
  };

  const handlePeriodChange = async (e) => {
    const periodNum = parseInt(e.target.value, 10);

    console.log("Domain period change:", {
      domainName: item.domainName,
      oldPeriod: period,
      newPeriod: periodNum,
      itemId: item._id,
      currentPrice: item.price,
      newPrice: getPriceForPeriod(periodNum),
    });

    try {
      setIsPeriodChanging(true);
      setPeriod(periodNum); // Update local state immediately for better UX

      // Call the parent's period change handler
      await onPeriodChange(item._id, periodNum, true);

      console.log("Period change successful");
      toast.success(t ? t("period_updated") : "Period updated successfully");
    } catch (error) {
      console.error("Error updating period:", error);
      // Revert local state on error
      setPeriod(item.period || 1);
      toast.error(t ? t("error_updating_period") : "Error updating period");
    } finally {
      setIsPeriodChanging(false);
    }
  };

  const handleRemoveItem = async () => {
    try {
      setIsUpdating(true);
      await cartService.removeDomainFromCart({ itemId: item._id });

      // Call the onRemove callback if provided, otherwise reload the page
      if (onRemove) {
        onRemove(item._id);
      } else {
        window.location.reload();
      }

      toast.success(
        t ? t("domain_removed_from_cart") : "Domain removed from cart"
      );
    } catch (error) {
      console.error("Error removing domain from cart:", error);
      toast.error(
        t ? t("error_removing_item") : "Error removing item from cart"
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="relative bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm hover:shadow-md transition-shadow">
      {/* Remove Button - Top Right */}
      <button
        onClick={handleRemoveItem}
        disabled={isUpdating}
        className="absolute top-3 right-3 text-red-500 hover:text-red-700 p-1 rounded-md hover:bg-red-50 transition-colors"
        title={t ? t("domain.remove_from_cart") : "Supprimer du panier"}
      >
        <TrashIcon width={18} strokeWidth={2} />
      </button>

      <div className="flex flex-col lg:flex-row lg:items-center gap-4 pr-10">
        {/* Domain Info Section */}
        <div className="flex items-center gap-3 flex-1">
          <div className="bg-blue-100 p-2 rounded-lg">
            <Globe className="h-5 w-5 text-blue-600" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-gray-900 text-lg">
                {item.domainName}
              </h3>
              <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                {t ? t("domain.available") : "Available"}
              </span>
            </div>
            <p className="text-sm text-gray-600">
              {t ? t("domainWrapper.registration") : "Domain Registration"}
            </p>
            <div className="flex items-baseline gap-2 mt-2">
              <span className="text-xl font-bold text-gray-900">
                {getPriceForPeriod(period).toFixed(2)}
              </span>
              <span className="text-base font-medium text-gray-900">MAD</span>
              <span className="text-sm text-gray-500">
                {period === 1 ? "/ an" : `/ ${period} ans`}
              </span>
            </div>
          </div>
        </div>

        {/* Period Selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t ? t("period") : "Période"}
          </label>
          <select
            value={period}
            onChange={handlePeriodChange}
            disabled={isPeriodChanging || isUpdating}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white min-w-[120px]"
          >
            {availablePeriods.map((periodOption) => (
              <option key={periodOption} value={periodOption}>
                {periodOption} {periodOption === 1 ? "an" : "ans"}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Contact Management Button */}
      {user && user.email && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => setShowContactModal(true)}
            className="flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors hover:bg-blue-50 px-3 py-2 rounded-md"
          >
            <Users className="h-4 w-4" />
            <span>
              {t ? t("domainWrapper.manage_contacts") : "Manage Contacts"}
            </span>
          </button>
        </div>
      )}

      {/* Domain Options */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">
          {t ? t("domain.options") : "Options de Domaine"}
        </h4>

        {/* ID Protect Option */}
        <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id={`privacy-${item._id}`}
              checked={localPrivacyProtection}
              onChange={(e) => handlePrivacyToggle(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div>
              <label
                htmlFor={`privacy-${item._id}`}
                className="text-sm font-medium text-blue-900 cursor-pointer"
              >
                {t ? t("domain.id_protect") : "ID Protect"}
              </label>
              <p className="text-xs text-blue-700 mt-1">
                {t
                  ? t("domain.id_protect_short_desc")
                  : "Protégez vos données personnelles dans le WHOIS"}
              </p>
            </div>
          </div>
          <div className="text-right">
            <span className="text-sm font-semibold text-blue-800">
              {t ? t("domain.id_protect_price") : "39.00 DH HT/an"}
            </span>
            <br />
            <button
              type="button"
              className="text-xs text-blue-600 hover:text-blue-800 underline mt-1"
              onClick={() => window.open("/domains/id-protect", "_blank")}
            >
              {t ? t("domain.see_more_details") : "Voir plus de détails"}
            </button>
          </div>
        </div>
      </div>

      {/* Contact Management Modal */}
      <DomainContactModal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
      />
    </div>
  );
}

export default DomainCartItem;
