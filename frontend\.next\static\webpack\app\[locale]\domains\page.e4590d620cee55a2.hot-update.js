"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/domains/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const [privacyProtection, setPrivacyProtection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Privacy protection option\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period,\n                privacyProtection: privacyProtection,\n                autoRenew: false\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-gray-700 mb-3\",\n                            children: t ? t(\"domain.options\") : \"Domain Options\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"privacy-protection\",\n                                        checked: privacyProtection,\n                                        onChange: (e)=>setPrivacyProtection(e.target.checked),\n                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"privacy-protection\",\n                                                className: \"text-sm text-gray-700 font-medium\",\n                                                children: t ? t(\"domain.id_protect\") : \"ID Protect\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    t ? t(\"domain.id_protect_description\") : \"Prot\\xe9gez vos donn\\xe9es personnelles. Cette option vous permet de cacher vos informations whois\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-blue-600 ml-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            t ? t(\"domain.id_protect_price\") : \"39.00 DH HT/an\",\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"text-blue-600 hover:text-blue-800 underline ml-1\",\n                                                        onClick: ()=>router.push(\"/domains/id-protect\"),\n                                                        children: t ? t(\"domain.see_more_details\") : \"Voir plus de d\\xe9tails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 262,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 273,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 266,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"5zr13Qy96GZ7/FKjnDvzSL6cdak=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});