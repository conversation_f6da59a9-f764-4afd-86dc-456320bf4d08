{"c": ["app/[locale]/layout", "app/layout", "app/[locale]/(sharedPages)/web-development/page", "app/[locale]/page", "app/[locale]/(sharedPages)/auth/login/page", "app/[locale]/(sharedPages)/hosting/page", "app/[locale]/(sharedPages)/hosting/vps/page", "app/[locale]/(sharedPages)/hosting/dedicated/page", "app/[locale]/client/layout", "app/[locale]/client/cart/page", "app/[locale]/client/domains/page", "app/[locale]/client/ssl-certificates/page", "app/[locale]/client/hosting-plans/page", "app/[locale]/client/web-development/page", "app/admin/page", "app/admin/layout", "app/admin/notification-settings/page", "app/admin/jobs/page", "app/admin/orders/page", "app/admin/packages/page", "app/admin/users/page", "app/admin/activity-logs/page", "app/[locale]/client/profile/page", "webpack", "_app-pages-browser_src_components_hosting_hPackagesPlanGrid_jsx", "_app-pages-browser_src_components_home_services_jsx", "_app-pages-browser_src_components_home_otherServices_jsx", "_app-pages-browser_src_components_shared_contactForm2_jsx", "_app-pages-browser_src_components_shared_faq2_jsx", "_app-pages-browser_src_components_home_TestimonialsSection_jsx", "_app-pages-browser_src_components_home_cloudMoroc_jsx", "_app-pages-browser_src_components_home_companyIntro_jsx"], "r": [], "m": ["(app-pages-browser)/./src/components/DynamicMetadataClient.jsx", "(app-pages-browser)/./src/components/home/<USER>", "(app-pages-browser)/./src/components/home/<USER>"]}