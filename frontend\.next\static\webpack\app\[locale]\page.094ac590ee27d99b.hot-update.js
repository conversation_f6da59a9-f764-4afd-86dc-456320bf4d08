"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const [privacyProtection, setPrivacyProtection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Privacy protection option\n    const [autoRenew, setAutoRenew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Auto-renewal option\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 219,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 401,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 230,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"0dlu9g0/oDsgZKGxTTSKKJ9GQXc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});