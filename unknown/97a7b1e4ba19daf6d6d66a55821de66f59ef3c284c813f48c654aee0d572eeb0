"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the API\n                try {\n                    var _detailsRes_data;\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetails(userDomain.name);\n                    console.log(\"Domain details from API:\", detailsRes.data);\n                    // Combine user domain data with API details\n                    const combinedDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"Loading nameservers...\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        // Add API details if available\n                        apiDetails: ((_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain) || null,\n                        // Default contact structure (will be populated from user contacts later)\n                        contacts: {\n                            registrant: {\n                                name: \"Loading...\",\n                                email: \"Loading...\",\n                                phone: \"Loading...\",\n                                address: \"Loading...\"\n                            },\n                            admin: {\n                                name: \"Loading...\",\n                                email: \"Loading...\",\n                                phone: \"Loading...\",\n                                address: \"Loading...\"\n                            },\n                            technical: {\n                                name: \"Loading...\",\n                                email: \"Loading...\",\n                                phone: \"Loading...\",\n                                address: \"Loading...\"\n                            }\n                        },\n                        // Default DNS records (placeholder)\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"Loading...\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(combinedDomain);\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"moha1280036.earth.orderbox-dns.com\",\n                            \"moha1280036.mars.orderbox-dns.com\",\n                            \"moha1280036.mercury.orderbox-dns.com\",\n                            \"moha1280036.venus.orderbox-dns.com\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.togglePrivacyProtection(id, value);\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling privacy protection\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(domain.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(domain.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.registrationDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.expiryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 402,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.privacyProtection,\n                                                                            onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                            color: \"blue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: dt(\"manage_dns_records\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                children: t(\"add_record\", {\n                                                                    defaultValue: \"Add Record\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"type\", {\n                                                                                    defaultValue: \"Type\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"name\", {\n                                                                                    defaultValue: \"Name\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"content\", {\n                                                                                    defaultValue: \"Content\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"ttl\", {\n                                                                                    defaultValue: \"TTL\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                                children: t(\"actions\", {\n                                                                                    defaultValue: \"Actions\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 490,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                    children: record.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 498,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 501,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.ttl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-right\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"text\",\n                                                                                        className: \"text-blue-600\",\n                                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                        children: t(\"edit\", {\n                                                                                            defaultValue: \"Edit\"\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 511,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, record.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.registrant.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.admin.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.technical.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.technical.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.technical.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.technical.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 595,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});