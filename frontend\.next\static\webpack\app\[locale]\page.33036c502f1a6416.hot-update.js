"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period,\n                privacyProtection: privacyProtection,\n                autoRenew: false\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 219,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 401,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 230,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"CnbqHtnQ24xX9c2xLqgEGfP4p/w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});