"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const [privacyProtection, setPrivacyProtection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Privacy protection option\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period,\n                privacyProtection: privacyProtection,\n                autoRenew: false\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-gray-700 mb-3\",\n                            children: t ? t(\"domain.options\") : \"Domain Options\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"privacy-protection\",\n                                            checked: privacyProtection,\n                                            onChange: (e)=>setPrivacyProtection(e.target.checked),\n                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"privacy-protection\",\n                                            className: \"text-sm text-gray-600\",\n                                            children: t ? t(\"domain.privacy_protection\") : \"Privacy Protection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"auto-renewal\",\n                                            checked: autoRenew,\n                                            onChange: (e)=>setAutoRenew(e.target.checked),\n                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"auto-renewal\",\n                                            className: \"text-sm text-gray-600\",\n                                            children: t ? t(\"domain.auto_renewal\") : \"Auto Renewal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 259,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 270,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 263,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"5zr13Qy96GZ7/FKjnDvzSL6cdak=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});