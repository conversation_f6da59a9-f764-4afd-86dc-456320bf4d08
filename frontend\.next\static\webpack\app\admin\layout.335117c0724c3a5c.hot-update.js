"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css":
/*!************************************************************!*\
  !*** ./node_modules/react-toastify/dist/ReactToastify.css ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"98f42cfef5aa\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10b2FzdGlmeS9kaXN0L1JlYWN0VG9hc3RpZnkuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtdG9hc3RpZnkvZGlzdC9SZWFjdFRvYXN0aWZ5LmNzcz83ODc0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOThmNDJjZmVmNWFhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"c5a48689f7ce\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/OGRlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImM1YTQ4Njg5ZjdjZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: function() { return /* binding */ API_GOOGLE_MAP_KEY; },\n/* harmony export */   BACKEND_URL: function() { return /* binding */ BACKEND_URL; },\n/* harmony export */   COOKIE_DOMAIN: function() { return /* binding */ COOKIE_DOMAIN; },\n/* harmony export */   FRONTEND_URL: function() { return /* binding */ FRONTEND_URL; },\n/* harmony export */   REACT_APP_GG_APP_ID: function() { return /* binding */ REACT_APP_GG_APP_ID; }\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = false;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29uZmlnL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsTUFBTUEsYUFBYTtBQUNuQixNQUFNQyxjQUFjO0FBRXBCLE1BQU1DLFVBQVU7QUFDaEIsTUFBTUMsV0FBVztBQUVqQixNQUFNQyxTQUFTO0FBQ1IsTUFBTUMsY0FBY0QsU0FBU0YsVUFBVUYsV0FBVztBQUNsRCxNQUFNTSxlQUFlRixTQUFTRCxXQUFXRixZQUFZO0FBQ3JELE1BQU1NLGdCQUFnQkgsU0FBUywwQkFBMEIsWUFBWTtBQUVyRSxNQUFNSSxzQkFDWCwyRUFBMkU7QUFDdEUsTUFBTUMscUJBQXFCLDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcz9iMTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhY2tlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6NTAwMlwiO1xyXG5jb25zdCBmcm9udGVuZERldiA9IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCI7XHJcblxyXG5jb25zdCBiYWNrZW5kID0gXCJodHRwczovL2FwaS56dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5jb25zdCBmcm9udGVuZCA9IFwiaHR0cHM6Ly96dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5cclxuY29uc3QgaXNQcm9kID0gZmFsc2U7XHJcbmV4cG9ydCBjb25zdCBCQUNLRU5EX1VSTCA9IGlzUHJvZCA/IGJhY2tlbmQgOiBiYWNrZW5kRGV2O1xyXG5leHBvcnQgY29uc3QgRlJPTlRFTkRfVVJMID0gaXNQcm9kID8gZnJvbnRlbmQgOiBmcm9udGVuZERldjtcclxuZXhwb3J0IGNvbnN0IENPT0tJRV9ET01BSU4gPSBpc1Byb2QgPyBcIi56dGVjaGVuZ2luZWVyaW5nLmNvbVwiIDogXCJsb2NhbGhvc3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBSRUFDVF9BUFBfR0dfQVBQX0lEID1cclxuICBcIjQ4MDk4NzM4NDQ1OS1oM2NpZTJ2Y3NocDA5dnBodXZuc2hjY3FwcmNvM2Ziby5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbVwiO1xyXG5leHBvcnQgY29uc3QgQVBJX0dPT0dMRV9NQVBfS0VZID0gXCJBSXphU3lBNXBHeTNVRUt3YmdqVVktNzJSbW9SN25wRXExYl91ZjBcIjtcclxuIl0sIm5hbWVzIjpbImJhY2tlbmREZXYiLCJmcm9udGVuZERldiIsImJhY2tlbmQiLCJmcm9udGVuZCIsImlzUHJvZCIsIkJBQ0tFTkRfVVJMIiwiRlJPTlRFTkRfVVJMIiwiQ09PS0lFX0RPTUFJTiIsIlJFQUNUX0FQUF9HR19BUFBfSUQiLCJBUElfR09PR0xFX01BUF9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/config/constant.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"(app-pages-browser)/./src/app/services/authService.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Create a Provider Component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user exists in localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            await checkAuth();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].checkAuth();\n            const updatedUser = response.data.user;\n            console.log(\"Fetched user:\", updatedUser);\n            // Update only if the data is different\n            if (JSON.stringify(updatedUser) !== localStorage.getItem(\"user\")) {\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                document.cookie = \"role=\".concat(updatedUser.role, \"; max-age=604800; path=/; secure\");\n            }\n            setUser(updatedUser);\n            return updatedUser;\n        } catch (err) {\n            console.error(\"Auth check failed:\", err);\n            setUser(null);\n            localStorage.removeItem(\"user\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle authentication error\n    const handleAuthError = (error)=>{\n        var _error_response_data, _error_response;\n        const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An unexpected error occurred\";\n        console.error(error);\n        return error;\n    };\n    // Login function\n    const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const loginRes = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].login(credentials);\n            const userData = loginRes.data.user;\n            setUser(userData);\n            // Store user in localStorage\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n            document.cookie = \"role=\".concat(userData.role, \"; max-age=604800; path=/; secure\");\n            // Check for pending cart items\n            const pendingItemJson = localStorage.getItem(\"pendingCartItem\");\n            if (pendingItemJson) {\n                try {\n                    // We'll handle this in a separate function after login completes\n                    // Just mark that we have a pending item\n                    loginRes.data.hasPendingCartItem = true;\n                } catch (cartError) {\n                    console.error(\"Error handling pending cart item:\", cartError);\n                }\n            }\n            return loginRes;\n        } catch (error) {\n            var _error_response, _error_response1;\n            const detailedError = {\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n            };\n            throw detailedError;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].logout();\n            // Clear user from localStorage\n            localStorage.removeItem(\"user\");\n            // Clear cookies on logout\n            document.cookie = \"role=; Max-Age=0; path:/\";\n            document.cookie = \"refresh_token=; Max-Age=0; path=/;\";\n            document.cookie = \"token=; Max-Age=0; path=/;\";\n            setUser(null);\n            router.refresh();\n            router.push(\"/auth/login\"); // Redirect to login page after logout\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.log(\"Logout error:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Compute if user is authenticated\n    const isAuthenticated = !!user;\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            loading,\n            login,\n            logout,\n            checkAuth,\n            cartCount,\n            setCartCount,\n            isAuthenticated\n        }), [\n        user,\n        loading,\n        cartCount,\n        checkAuth,\n        isAuthenticated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\context\\\\AuthContext.jsx\",\n        lineNumber: 143,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"vGpTtn+k/cSLMBUJ8G2NeTQqYd0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook for using AuthContext\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(app-pages-browser)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, config);\n    },\n    post: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config);\n    },\n    put: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config);\n    },\n    delete: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config);\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2FwaVNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7QUFBNEM7QUFFNUMsTUFBTUMsYUFBYTtJQUNqQkMsS0FBSyxTQUFDQztZQUFLQywwRUFBUyxDQUFDO2VBQU1KLHNEQUFhQSxDQUFDRSxHQUFHLENBQUNDLEtBQUtDOztJQUVsREMsTUFBTSxTQUFDRjtZQUFLRyx3RUFBTyxDQUFDLEdBQUdGLDBFQUFTLENBQUM7ZUFBTUosc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUY7O0lBRXJFRyxLQUFLLFNBQUNKO1lBQUtHLHdFQUFPLENBQUMsR0FBR0YsMEVBQVMsQ0FBQztlQUFNSixzREFBYUEsQ0FBQ08sR0FBRyxDQUFDSixLQUFLRyxNQUFNRjs7SUFFbkVJLFFBQVEsU0FBQ0w7WUFBS0MsMEVBQVMsQ0FBQztlQUFNSixzREFBYUEsQ0FBQ1EsTUFBTSxDQUFDTCxLQUFLQzs7QUFDMUQ7QUFFQSwrREFBZUgsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSBcIi4vYXhpb3NJbnN0YW5jZVwiO1xyXG5cclxuY29uc3QgYXBpU2VydmljZSA9IHtcclxuICBnZXQ6ICh1cmwsIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIGNvbmZpZyksXHJcblxyXG4gIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gIHB1dDogKHVybCwgZGF0YSA9IHt9LCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5wdXQodXJsLCBkYXRhLCBjb25maWcpLFxyXG5cclxuICBkZWxldGU6ICh1cmwsIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmRlbGV0ZSh1cmwsIGNvbmZpZyksXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBhcGlTZXJ2aWNlO1xyXG4iXSwibmFtZXMiOlsiYXhpb3NJbnN0YW5jZSIsImFwaVNlcnZpY2UiLCJnZXQiLCJ1cmwiLCJjb25maWciLCJwb3N0IiwiZGF0YSIsInB1dCIsImRlbGV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/apiService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!***********************************************!*\
  !*** ./src/components/home/<USER>
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Edit,Info,Loader2,MessageSquare,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../app/context/AuthContext */ \"(app-pages-browser)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../app/services/adminService */ \"(app-pages-browser)/./src/app/services/adminService.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _app_config_constant__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../app/config/constant */ \"(app-pages-browser)/./src/app/config/constant.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n // Assuming path\n // Assuming path\n\n\n\nconst Notifications = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const notificationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const socket = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timeAgo = (dateString)=>{\n        if (!dateString) return \"\";\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            console.error(\"Error formatting date:\", e);\n            return dateString; // fallback to original string\n        }\n    };\n    // We've removed the debug functions since they're no longer needed in production\n    // If you need to debug in the future, you can add them back\n    // State to track socket connection status\n    const [socketStatus, setSocketStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        connected: false,\n        joinedAdminRoom: false,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n        console.log(\"[SOCKET DEBUG-FRONTEND] Component mounted, user:\", user ? \"available\" : \"not available\");\n        console.log(\"[SOCKET DEBUG-FRONTEND] Socket URL:\", process.env.NEXT_PUBLIC_SOCKET_URL || \"not available\");\n        // Only proceed if we have a user\n        if (user) {\n            console.log(\"[SOCKET DEBUG-FRONTEND] Initializing socket connection (automatic in all environments)\");\n            // Ensure we don't have an existing connection\n            if (socket.current) {\n                console.log(\"[SOCKET DEBUG-FRONTEND] Cleaning up existing socket connection\");\n                socket.current.disconnect();\n                socket.current = null;\n            }\n            // Use the BACKEND_URL from our constants file for socket connection\n            // This ensures we connect to the correct backend in both dev and prod environments\n            const socketUrl = _app_config_constant__WEBPACK_IMPORTED_MODULE_6__.BACKEND_URL;\n            console.log(\"[SOCKET DEBUG-FRONTEND] Using socket URL for auto-connection:\", socketUrl);\n            // Create new socket connection with explicit options\n            socket.current = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(socketUrl, {\n                query: {\n                    adminId: user._id\n                },\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 30000,\n                withCredentials: true,\n                forceNew: true,\n                autoConnect: true,\n                debug: !_app_config_constant__WEBPACK_IMPORTED_MODULE_6__.isProd // Enable debug mode in development\n            });\n            // Log connection attempt\n            console.log(\"[SOCKET DEBUG-FRONTEND] Attempting to auto-connect to \".concat(socketUrl, \" with transports:\"), [\n                \"websocket\",\n                \"polling\"\n            ]);\n            socket.current.on(\"connect\", ()=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket connected for notifications\");\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket ID:\", socket.current.id);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Connected with user ID:\", user._id);\n                // Update socket status\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: true,\n                        error: null\n                    }));\n                // Add a small delay before joining the admin room (ensures connection is fully established)\n                setTimeout(()=>{\n                    // Join admin room\n                    socket.current.emit(\"join-admin\");\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Emitted join-admin event\");\n                    // Test notification reception\n                    socket.current.on(\"notification\", (notification)=>{\n                        console.log(\"[SOCKET DEBUG-FRONTEND] NOTIFICATION RECEIVED:\", notification);\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Notification type:\", notification.type);\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Notification title:\", notification.title);\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Notification message:\", notification.message);\n                    });\n                }, 500);\n            });\n            // Listen for 'notification' event from the backend\n            socket.current.on(\"notification\", (newNotification)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] New notification received via socket:\");\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification type:\", newNotification.type);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification title:\", newNotification.title);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification message:\", newNotification.message);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification ID:\", newNotification._id);\n                if (true) {\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Full notification data:\", newNotification);\n                }\n                // Add to the beginning of the list to show newest first\n                setNotifications((prevNotifications)=>{\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Adding notification to state, current count:\", prevNotifications.length);\n                    return [\n                        newNotification,\n                        ...prevNotifications\n                    ].slice(0, 20); // Keep a reasonable limit, e.g., 20\n                });\n                if (!newNotification.isRead) {\n                    setUnreadCount((prevCount)=>{\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Incrementing unread count from\", prevCount, \"to\", prevCount + 1);\n                        return prevCount + 1;\n                    });\n                }\n            });\n            // Fetch initial notifications and unread count on mount\n            const fetchInitialNotifs = async ()=>{\n                setLoading(true);\n                setError(null);\n                try {\n                    var _response_data_notifications;\n                    const response = await _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__.adminService.getAdminNotifications({\n                        page: 1,\n                        limit: 20,\n                        unreadOnly: false\n                    });\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Initial notifications fetched:\", ((_response_data_notifications = response.data.notifications) === null || _response_data_notifications === void 0 ? void 0 : _response_data_notifications.length) || 0);\n                    setNotifications(response.data.notifications || []);\n                    setUnreadCount(response.data.unreadCount || 0);\n                } catch (err) {\n                    console.error(\"[SOCKET DEBUG-FRONTEND] Failed to fetch initial notifications:\", err);\n                    setError(\"Failed to load notifications.\");\n                    setNotifications([]); // Clear notifications on error\n                    setUnreadCount(0);\n                } finally{\n                    setLoading(false);\n                }\n            };\n            fetchInitialNotifs();\n            socket.current.on(\"disconnect\", (reason)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket disconnected, reason:\", reason);\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: false,\n                        joinedAdminRoom: false\n                    }));\n            });\n            socket.current.on(\"connect_error\", (err)=>{\n                console.error(\"[SOCKET DEBUG-FRONTEND] Socket connection error:\", err.message);\n                console.error(\"[SOCKET DEBUG-FRONTEND] Socket URL that failed:\", socketUrl);\n                console.error(\"[SOCKET DEBUG-FRONTEND] Error details:\", err);\n                // Log environment information to help with debugging\n                console.log(\"[SOCKET DEBUG-FRONTEND] Environment:\", _app_config_constant__WEBPACK_IMPORTED_MODULE_6__.isProd ? \"Production\" : \"Development\");\n                console.log(\"[SOCKET DEBUG-FRONTEND] BACKEND_URL:\", _app_config_constant__WEBPACK_IMPORTED_MODULE_6__.BACKEND_URL);\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: false,\n                        error: err.message\n                    }));\n                // Attempt to reconnect after a delay\n                setTimeout(()=>{\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Attempting to reconnect...\");\n                    if (socket.current) {\n                        socket.current.connect();\n                    }\n                }, 5000); // Try to reconnect after 5 seconds\n            });\n            socket.current.on(\"error\", (err)=>{\n                console.error(\"[SOCKET DEBUG-FRONTEND] Socket error:\", err);\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        error: err.message\n                    }));\n            });\n            // Listen for admin room join confirmation\n            socket.current.on(\"admin-room-joined\", (data)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Received admin-room-joined confirmation:\", data);\n                if (data.success) {\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Successfully joined admin room. Room size: \".concat(data.roomSize));\n                    setSocketStatus((prev)=>({\n                            ...prev,\n                            joinedAdminRoom: true\n                        }));\n                }\n            });\n            // Log reconnection attempts\n            socket.current.io.on(\"reconnect_attempt\", (attempt)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket reconnection attempt:\", attempt);\n            });\n            socket.current.io.on(\"reconnect\", (attempt)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket reconnected after\", attempt, \"attempts\");\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: true\n                    }));\n            });\n        } else {\n            console.log(\"[SOCKET DEBUG-FRONTEND] User not available, skipping socket connection\");\n        }\n        return ()=>{\n            if (socket.current) {\n                console.log(\"[SOCKET DEBUG-FRONTEND] Cleaning up socket on component unmount\");\n                socket.current.disconnect();\n            }\n        };\n    }, [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted) return;\n        const handleClickOutside = (event)=>{\n            if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        if (isOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isOpen,\n        isMounted\n    ]);\n    const toggleNotifications = ()=>{\n        setIsOpen(!isOpen);\n    };\n    const handleMarkAsRead = async (notificationId)=>{\n        try {\n            await _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__.adminService.markNotificationAsRead(notificationId);\n            setNotifications((prev)=>prev.map((n)=>n._id === notificationId ? {\n                        ...n,\n                        isRead: true\n                    } : n));\n            setUnreadCount((prev)=>Math.max(0, prev - 1));\n        } catch (err) {\n            console.error(\"Failed to mark notification as read:\", err);\n        // Optionally show a toast error\n        }\n    };\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__.adminService.markAllNotificationsAsRead();\n            // Update local state to mark all notifications as read\n            setNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setUnreadCount(0);\n            console.log(\"[NOTIFICATIONS] Successfully marked all notifications as read:\", response);\n        } catch (err) {\n            console.error(\"[NOTIFICATIONS] Failed to mark all notifications as read:\", err);\n        // Optionally show a toast error\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Function to manually reconnect the socket\n    const handleReconnect = ()=>{\n        if (!socket.current) {\n            return;\n        }\n        console.log(\"[SOCKET DEBUG-FRONTEND] Manual reconnection attempt\");\n        // First disconnect if already connected\n        if (socket.current.connected) {\n            socket.current.disconnect();\n        }\n        // Then try to reconnect\n        socket.current.connect();\n        // Update UI to show connecting state\n        setSocketStatus((prev)=>({\n                ...prev,\n                connected: false,\n                error: null\n            }));\n    };\n    // We've removed the debug functions since they're no longer needed in production\n    // If you need to debug in the future, you can add them back\n    const handleNotificationClick = (notification)=>{\n        if (!notification.isRead) {\n            handleMarkAsRead(notification._id);\n        }\n        // No redirect on click\n        setIsOpen(false); // Optionally close dropdown after marking as read\n    };\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative p-2 rounded-full hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 300,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, undefined);\n    }\n    const getNotificationIcon = (type)=>{\n        // Assuming notification types like 'new_ticket', 'ticket_status_update', 'order_update', etc.\n        // You might want to map these types to specific icons and colors.\n        switch(type){\n            case \"new_ticket\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 311,\n                    columnNumber: 16\n                }, undefined);\n            case \"ticket_status_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 313,\n                    columnNumber: 16\n                }, undefined);\n            case \"ticket_updated\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 16\n                }, undefined);\n            case \"ticket_deleted\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 317,\n                    columnNumber: 16\n                }, undefined);\n            case \"order_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-amber-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 319,\n                    columnNumber: 16\n                }, undefined);\n            case \"ssl_expiry\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 321,\n                    columnNumber: 16\n                }, undefined);\n            case \"abandoned_cart\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 323,\n                    columnNumber: 16\n                }, undefined);\n            case \"custom_notification\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 325,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 327,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    // Function to get enhanced notification message with context\n    const getEnhancedMessage = (notification)=>{\n        // For order notifications, add more context\n        if (notification.type === \"order_update\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: notification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            \"By: \",\n                            notification.actionBy.firstName,\n                            \" \",\n                            notification.actionBy.lastName,\n                            notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, undefined);\n        }\n        // For SSL notifications, add more context\n        if (notification.type === \"ssl_expiry\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: notification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, undefined),\n                    notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            \"By: \",\n                            notification.actionBy.firstName,\n                            \" \",\n                            notification.actionBy.lastName,\n                            notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 354,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Default display for other notification types\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: notification.message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, undefined),\n                notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: [\n                        \"By: \",\n                        notification.actionBy.firstName,\n                        \" \",\n                        notification.actionBy.lastName,\n                        notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 368,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n            lineNumber: 365,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: notificationRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative p-2 rounded-full hover:bg-gray-100 border border-gray-100\",\n                \"aria-label\": \"Notifications\",\n                onClick: toggleNotifications,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-600 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 flex size-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative inline-flex size-2 rounded-full bg-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-custom-heavy border border-gray-200 z-50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200 bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-700\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full\",\n                                        children: [\n                                            unreadCount,\n                                            \" new\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined),\n                            socketStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mt-2 text-xs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleReconnect();\n                                    },\n                                    className: \"px-2 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors\",\n                                    children: \"Reconnect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, undefined),\n                            !_app_config_constant__WEBPACK_IMPORTED_MODULE_6__.isProd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 pt-2 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Socket:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        socketStatus.connected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"Connected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"Disconnected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        socketStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500 ml-2\",\n                                            children: [\n                                                \"Error: \",\n                                                socketStatus.error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto hide-scrollbar\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-6 h-6 animate-spin text-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 448,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 447,\n                            columnNumber: 15\n                        }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 px-4 text-center text-red-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-8 h-8 mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 451,\n                            columnNumber: 15\n                        }, undefined) : notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-100\",\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 hover:bg-gray-50 transition-colors cursor-pointer \".concat(!notification.isRead ? \"bg-blue-50/50\" : \"\"),\n                                    onClick: ()=>handleNotificationClick(notification),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mt-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(!notification.isRead ? \"bg-primary/10\" : \"bg-gray-100\"),\n                                                    children: getNotificationIcon(notification.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium truncate \".concat(!notification.isRead ? \"text-primary\" : \"text-gray-800\"),\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 whitespace-nowrap ml-2\",\n                                                                children: timeAgo(notification.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    getEnhancedMessage(notification)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, notification._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 456,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Edit_Info_Loader2_MessageSquare_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"We\",\n                                        \"'\",\n                                        \"ll notify you when something arrives\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 492,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMarkAllAsRead,\n                                    disabled: unreadCount === 0 || loading,\n                                    className: \"text-sm text-primary hover:text-blue-700 font-medium transition-colors py-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        router.push(\"/admin/notifications\");\n                                        setIsOpen(false);\n                                    },\n                                    className: \"text-sm text-gray-500 hover:text-gray-700 transition-colors py-1\",\n                                    children: \"View all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Notifications, \"Hh26TcFbdNve32HsdRE4wKtdHqM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Notifications;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Notifications);\nvar _c;\n$RefreshReg$(_c, \"Notifications\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});